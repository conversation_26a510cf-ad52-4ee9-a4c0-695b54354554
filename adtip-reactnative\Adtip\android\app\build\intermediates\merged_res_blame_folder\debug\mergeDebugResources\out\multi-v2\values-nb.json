{"logs": [{"outputFile": "com.adtip.app.adtip_app-mergeDebugResources-4:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6a4b12df2937b548059e098326cd7bcc\\transformed\\jetified-play-services-wallet-18.1.3\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "70", "endOffsets": "272"}, "to": {"startLines": "265", "startColumns": "4", "startOffsets": "21104", "endColumns": "74", "endOffsets": "21174"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0cb78797bbebcc5583f5b3648f5d6ccd\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "86,162,163,164", "startColumns": "4,4,4,4", "startOffsets": "7493,13331,13432,13544", "endColumns": "109,100,111,96", "endOffsets": "7598,13427,13539,13636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\91b5c4a59b6d6a7985119f29ba44ddae\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,257,328,408,486,580,677", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "126,188,252,323,403,481,575,672,743"}, "to": {"startLines": "133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11356,11432,11494,11558,11629,11709,11787,11881,11978", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "11427,11489,11553,11624,11704,11782,11876,11973,12044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9eda951e918e947e7d9ddd1ba9b2c563\\transformed\\jetified-media3-session-1.4.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,225,290,387,486,563,647,726,818,904,969,1074,1161,1257,1331,1420,1498,1590,1666,1736,1815,1898,1996", "endColumns": "72,96,64,96,98,76,83,78,91,85,64,104,86,95,73,88,77,91,75,69,78,82,97,103", "endOffsets": "123,220,285,382,481,558,642,721,813,899,964,1069,1156,1252,1326,1415,1493,1585,1661,1731,1810,1893,1991,2095"}, "to": {"startLines": "89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,182,183,184,185,186,187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7711,7907,8004,8069,8166,8265,8342,8426,8505,8597,8683,8748,8853,8940,9036,9110,9199,9277,14808,14884,14954,15033,15116,15214", "endColumns": "72,96,64,96,98,76,83,78,91,85,64,104,86,95,73,88,77,91,75,69,78,82,97,103", "endOffsets": "7779,7999,8064,8161,8260,8337,8421,8500,8592,8678,8743,8848,8935,9031,9105,9194,9272,9364,14879,14949,15028,15111,15209,15313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ef18ad19ff26599d64ec0eff4ea7dc70\\transformed\\appcompat-1.7.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,259", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "928,1031,1126,1240,1326,1426,1539,1616,1691,1782,1875,1969,2063,2163,2256,2351,2449,2540,2631,2709,2812,2910,3006,3110,3209,3310,3463,20643", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "1026,1121,1235,1321,1421,1534,1611,1686,1777,1870,1964,2058,2158,2251,2346,2444,2535,2626,2704,2807,2905,3001,3105,3204,3305,3458,3555,20718"}}, {"source": "C:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,166,219,274,316,378,427,484,531,577", "endColumns": "55,54,52,54,41,61,48,56,46,45,52", "endOffsets": "106,161,214,269,311,373,422,479,526,572,625"}, "to": {"startLines": "50,87,88,165,166,188,189,190,250,264,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3560,7603,7658,13641,13696,15318,15380,15429,20139,21058,21239", "endColumns": "55,54,52,54,41,61,48,56,46,45,52", "endOffsets": "3611,7653,7706,13691,13733,15375,15424,15481,20181,21099,21287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd11809084d63593099820b405d61701\\transformed\\material-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1025,1089,1181,1249,1309,1396,1460,1522,1586,1654,1719,1773,1882,1940,2002,2056,2131,2251,2333,2410,2500,2584,2664,2798,2876,2956,3079,3167,3245,3299,3350,3416,3484,3558,3629,3705,3776,3854,3924,3994,4094,4183,4261,4349,4439,4511,4583,4667,4718,4796,4862,4943,5026,5088,5152,5215,5284,5384,5488,5581,5681,5739,5794,5872,5956,6034", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "256,333,406,493,581,661,760,879,961,1020,1084,1176,1244,1304,1391,1455,1517,1581,1649,1714,1768,1877,1935,1997,2051,2126,2246,2328,2405,2495,2579,2659,2793,2871,2951,3074,3162,3240,3294,3345,3411,3479,3553,3624,3700,3771,3849,3919,3989,4089,4178,4256,4344,4434,4506,4578,4662,4713,4791,4857,4938,5021,5083,5147,5210,5279,5379,5483,5576,5676,5734,5789,5867,5951,6029,6101"}, "to": {"startLines": "19,53,54,55,56,57,65,66,67,90,91,161,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,251,260,261,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "767,3848,3925,3998,4085,4173,4979,5078,5197,7784,7843,13239,13738,13806,13866,13953,14017,14079,14143,14211,14276,14330,14439,14497,14559,14613,14688,15486,15568,15645,15735,15819,15899,16033,16111,16191,16314,16402,16480,16534,16585,16651,16719,16793,16864,16940,17011,17089,17159,17229,17329,17418,17496,17584,17674,17746,17818,17902,17953,18031,18097,18178,18261,18323,18387,18450,18519,18619,18723,18816,18916,18974,20186,20723,20807,20885", "endLines": "22,53,54,55,56,57,65,66,67,90,91,161,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,251,260,261,262", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "923,3920,3993,4080,4168,4248,5073,5192,5274,7838,7902,13326,13801,13861,13948,14012,14074,14138,14206,14271,14325,14434,14492,14554,14608,14683,14803,15563,15640,15730,15814,15894,16028,16106,16186,16309,16397,16475,16529,16580,16646,16714,16788,16859,16935,17006,17084,17154,17224,17324,17413,17491,17579,17669,17741,17813,17897,17948,18026,18092,18173,18256,18318,18382,18445,18514,18614,18718,18811,18911,18969,19024,20259,20802,20880,20952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8e1703580f38993d5096d25ba35ecdf1\\transformed\\jetified-media3-ui-1.4.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,482,672,758,843,921,1007,1095,1170,1234,1327,1418,1491,1558,1624,1694,1803,1913,2020,2093,2176,2252,2325,2428,2530,2594,2659,2712,2770,2818,2879,2949,3017,3083,3153,3217,3276,3340,3392,3452,3526,3600,3653", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,108,109,106,72,82,75,72,102,101,63,64,52,57,47,60,69,67,65,69,63,58,63,51,59,73,73,52,64", "endOffsets": "280,477,667,753,838,916,1002,1090,1165,1229,1322,1413,1486,1553,1619,1689,1798,1908,2015,2088,2171,2247,2320,2423,2525,2589,2654,2707,2765,2813,2874,2944,3012,3078,3148,3212,3271,3335,3387,3447,3521,3595,3648,3713"}, "to": {"startLines": "2,11,15,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,142,143,144,145,146,147,148,149,150,151,152,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,577,9369,9455,9540,9618,9704,9792,9867,9931,10024,10115,10188,10255,10321,10391,10500,10610,10717,10790,10873,10949,11022,11125,11227,11291,12049,12102,12160,12208,12269,12339,12407,12473,12543,12607,12666,12861,12913,12973,13047,13121,13174", "endLines": "10,14,18,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,142,143,144,145,146,147,148,149,150,151,152,155,156,157,158,159,160", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,108,109,106,72,82,75,72,102,101,63,64,52,57,47,60,69,67,65,69,63,58,63,51,59,73,73,52,64", "endOffsets": "375,572,762,9450,9535,9613,9699,9787,9862,9926,10019,10110,10183,10250,10316,10386,10495,10605,10712,10785,10868,10944,11017,11120,11222,11286,11351,12097,12155,12203,12264,12334,12402,12468,12538,12602,12661,12725,12908,12968,13042,13116,13169,13234"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e4f7aaf1dd3251b595a6d29fbf072499\\transformed\\core-1.16.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "58,59,60,61,62,63,64,263", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4253,4347,4449,4546,4645,4753,4859,20957", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "4342,4444,4541,4640,4748,4854,4974,21053"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfbd6dfbd7eb9930845c16c988ee8525\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,120", "endOffsets": "161,282"}, "to": {"startLines": "51,52", "startColumns": "4,4", "startOffsets": "3616,3727", "endColumns": "110,120", "endOffsets": "3722,3843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c9fc1356237c73a36541687fd82ee2b5\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,292,347,411,480,570,632,738,842,954,1004,1060,1167,1253,1292,1369,1402,1435,1488,1565,1604", "endColumns": "41,50,54,63,68,89,61,105,103,111,49,55,106,85,38,76,32,32,52,76,38,55", "endOffsets": "240,291,346,410,479,569,631,737,841,953,1003,1059,1166,1252,1291,1368,1401,1434,1487,1564,1603,1659"}, "to": {"startLines": "236,237,238,239,240,241,242,243,244,245,246,247,248,249,252,253,254,255,256,257,258,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19029,19075,19130,19189,19257,19330,19424,19490,19600,19708,19824,19878,19938,20049,20264,20307,20388,20425,20462,20519,20600,21179", "endColumns": "45,54,58,67,72,93,65,109,107,115,53,59,110,89,42,80,36,36,56,80,42,59", "endOffsets": "19070,19125,19184,19252,19325,19419,19485,19595,19703,19819,19873,19933,20044,20134,20302,20383,20420,20457,20514,20595,20638,21234"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\31cc88bd1a811e01477d23dfca8d3955\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "68,69,70,71,72,73,74,75,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5279,5385,5544,5670,5779,5935,6065,6185,6418,6572,6679,6840,6968,7110,7286,7353,7415", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "5380,5539,5665,5774,5930,6060,6180,6283,6567,6674,6835,6963,7105,7281,7348,7410,7488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6ee598c7bab1dd2859d5d01f2229eaf5\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "76", "startColumns": "4", "startOffsets": "6288", "endColumns": "129", "endOffsets": "6413"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f9b6829f7f71dcf7ed8c3a43ef2febe5\\transformed\\jetified-exoplayer-ui-2.19.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3340,3405", "endColumns": "64,65", "endOffsets": "3400,3466"}, "to": {"startLines": "153,154", "startColumns": "4,4", "startOffsets": "12730,12795", "endColumns": "64,65", "endOffsets": "12790,12856"}}]}]}