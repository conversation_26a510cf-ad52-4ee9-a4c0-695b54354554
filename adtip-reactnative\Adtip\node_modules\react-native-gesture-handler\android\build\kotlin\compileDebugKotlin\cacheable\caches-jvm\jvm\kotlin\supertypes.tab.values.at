/ Header Record For PersistentHashMapValueStorageW #com.facebook.react.BaseReactPackage2com.facebook.react.ViewManagerOnDemandReactPackage1 0com.swmansion.gesturehandler.core.GestureHandler9 8com.swmansion.gesturehandler.core.GestureHandler.Factory java.lang.Exception1 0com.swmansion.gesturehandler.core.GestureHandler9 8com.swmansion.gesturehandler.core.GestureHandler.Factory1 0com.swmansion.gesturehandler.core.GestureHandler9 8com.swmansion.gesturehandler.core.GestureHandler.Factory1 0com.swmansion.gesturehandler.core.GestureHandler9 8com.swmansion.gesturehandler.core.GestureHandler.Factory1 0com.swmansion.gesturehandler.core.GestureHandler9 8com.swmansion.gesturehandler.core.GestureHandler.FactoryX Wcom.swmansion.gesturehandler.core.NativeViewGestureHandler.NativeViewGestureHandlerHookX Wcom.swmansion.gesturehandler.core.NativeViewGestureHandler.NativeViewGestureHandlerHookX Wcom.swmansion.gesturehandler.core.NativeViewGestureHandler.NativeViewGestureHandlerHookX Wcom.swmansion.gesturehandler.core.NativeViewGestureHandler.NativeViewGestureHandlerHookX Wcom.swmansion.gesturehandler.core.NativeViewGestureHandler.NativeViewGestureHandlerHook1 0com.swmansion.gesturehandler.core.GestureHandler9 8com.swmansion.gesturehandler.core.GestureHandler.Factory1 0com.swmansion.gesturehandler.core.GestureHandler9 8com.swmansion.gesturehandler.core.GestureHandler.Factory kotlin.Enum1 0com.swmansion.gesturehandler.core.GestureHandler9 8com.swmansion.gesturehandler.core.GestureHandler.Factory1 0com.swmansion.gesturehandler.core.GestureHandler9 8com.swmansion.gesturehandler.core.GestureHandler.Factoryu -com.facebook.react.uimanager.ViewGroupManagerFcom.facebook.react.viewmanagers.RNGestureHandlerButtonManagerInterfaceo android.view.ViewGroupWcom.swmansion.gesturehandler.core.NativeViewGestureHandler.NativeViewGestureHandlerHook!  com.facebook.react.ReactRootView* )com.facebook.react.uimanager.events.Event7 6com.swmansion.gesturehandler.core.OnTouchEventListenerF Ecom.swmansion.gesturehandler.core.GestureHandlerInteractionControllern =com.swmansion.gesturehandler.NativeRNGestureHandlerModuleSpec/com.swmansion.common.GestureHandlerStateManager9 8com.swmansion.gesturehandler.core.GestureHandlerRegistry1 0com.swmansion.gesturehandler.core.GestureHandler- ,com.facebook.react.views.view.ReactViewGroupw -com.facebook.react.uimanager.ViewGroupManagerHcom.facebook.react.viewmanagers.RNGestureHandlerRootViewManagerInterface* )com.facebook.react.uimanager.events.Event* )com.facebook.react.uimanager.events.Event: 9com.swmansion.gesturehandler.core.ViewConfigurationHelperP Ocom.swmansion.gesturehandler.react.eventbuilders.GestureHandlerEventDataBuilderP Ocom.swmansion.gesturehandler.react.eventbuilders.GestureHandlerEventDataBuilderP Ocom.swmansion.gesturehandler.react.eventbuilders.GestureHandlerEventDataBuilderP Ocom.swmansion.gesturehandler.react.eventbuilders.GestureHandlerEventDataBuilderP Ocom.swmansion.gesturehandler.react.eventbuilders.GestureHandlerEventDataBuilderP Ocom.swmansion.gesturehandler.react.eventbuilders.GestureHandlerEventDataBuilderP Ocom.swmansion.gesturehandler.react.eventbuilders.GestureHandlerEventDataBuilderP Ocom.swmansion.gesturehandler.react.eventbuilders.GestureHandlerEventDataBuilderP Ocom.swmansion.gesturehandler.react.eventbuilders.GestureHandlerEventDataBuilder