{"logs": [{"outputFile": "com.adtip.app.adtip_app-mergeDebugResources-4:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\31cc88bd1a811e01477d23dfca8d3955\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,448,574,680,833,959,1070,1173,1319,1422,1575,1699,1842,1981,2045,2103", "endColumns": "101,152,125,105,152,125,110,102,145,102,152,123,142,138,63,57,76", "endOffsets": "294,447,573,679,832,958,1069,1172,1318,1421,1574,1698,1841,1980,2044,2102,2179"}, "to": {"startLines": "72,73,74,75,76,77,78,79,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5748,5854,6011,6141,6251,6408,6538,6653,6892,7042,7149,7306,7434,7581,7724,7792,7854", "endColumns": "105,156,129,109,156,129,114,106,149,106,156,127,146,142,67,61,80", "endOffsets": "5849,6006,6136,6246,6403,6533,6648,6755,7037,7144,7301,7429,7576,7719,7787,7849,7930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfbd6dfbd7eb9930845c16c988ee8525\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,121", "endOffsets": "161,283"}, "to": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3964,4075", "endColumns": "110,121", "endOffsets": "4070,4192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6ee598c7bab1dd2859d5d01f2229eaf5\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "80", "startColumns": "4", "startOffsets": "6760", "endColumns": "131", "endOffsets": "6887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c9fc1356237c73a36541687fd82ee2b5\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,248,295,353,418,487,598,661,804,905,1022,1076,1133,1242,1339,1379,1467,1503,1536,1590,1675,1715", "endColumns": "48,46,57,64,68,110,62,142,100,116,53,56,108,96,39,87,35,32,53,84,39,55", "endOffsets": "247,294,352,417,486,597,660,803,904,1021,1075,1132,1241,1338,1378,1466,1502,1535,1589,1674,1714,1770"}, "to": {"startLines": "240,241,242,243,244,245,246,247,248,249,250,251,252,253,258,259,260,261,262,263,264,284", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20052,20105,20156,20218,20287,20360,20475,20542,20689,20794,20915,20973,21034,21147,21573,21617,21709,21749,21786,21844,21933,23517", "endColumns": "52,50,61,68,72,114,66,146,104,120,57,60,112,100,43,91,39,36,57,88,43,59", "endOffsets": "20100,20151,20213,20282,20355,20470,20537,20684,20789,20910,20968,21029,21142,21243,21612,21704,21744,21781,21839,21928,21972,23572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0cb78797bbebcc5583f5b3648f5d6ccd\\transformed\\browser-1.8.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "90,164,165,166", "startColumns": "4,4,4,4", "startOffsets": "7935,13883,13983,14097", "endColumns": "104,99,113,101", "endOffsets": "8035,13978,14092,14194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e4f7aaf1dd3251b595a6d29fbf072499\\transformed\\core-1.16.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "61,62,63,64,65,66,67,278", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4628,4726,4833,4930,5029,5133,5237,23027", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "4721,4828,4925,5024,5128,5232,5349,23123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\91b5c4a59b6d6a7985119f29ba44ddae\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,566,648", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "125,186,251,324,403,476,561,643,716"}, "to": {"startLines": "135,136,137,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11859,11934,11995,12060,12133,12212,12285,12370,12452", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "11929,11990,12055,12128,12207,12280,12365,12447,12520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd11809084d63593099820b405d61701\\transformed\\material-1.12.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1128,1196,1292,1360,1423,1531,1591,1657,1713,1784,1844,1898,2024,2081,2143,2197,2272,2406,2491,2569,2664,2749,2830,2967,3051,3137,3270,3361,3439,3495,3550,3616,3690,3768,3839,3921,3993,4070,4150,4224,4331,4424,4497,4589,4685,4759,4835,4931,4983,5065,5132,5219,5306,5368,5432,5495,5565,5671,5787,5884,5998,6058,6117,6197,6280,6357", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "315,393,471,556,653,746,842,972,1056,1123,1191,1287,1355,1418,1526,1586,1652,1708,1779,1839,1893,2019,2076,2138,2192,2267,2401,2486,2564,2659,2744,2825,2962,3046,3132,3265,3356,3434,3490,3545,3611,3685,3763,3834,3916,3988,4065,4145,4219,4326,4419,4492,4584,4680,4754,4830,4926,4978,5060,5127,5214,5301,5363,5427,5490,5560,5666,5782,5879,5993,6053,6112,6192,6275,6352,6427"}, "to": {"startLines": "21,56,57,58,59,60,68,69,70,92,93,163,168,171,173,174,175,176,177,178,179,180,181,182,183,184,185,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,254,267,268,269", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "959,4197,4275,4353,4438,4535,5354,5450,5580,8115,8182,13787,14273,14491,14621,14729,14789,14855,14911,14982,15042,15096,15222,15279,15341,15395,15470,16341,16426,16504,16599,16684,16765,16902,16986,17072,17205,17296,17374,17430,17485,17551,17625,17703,17774,17856,17928,18005,18085,18159,18266,18359,18432,18524,18620,18694,18770,18866,18918,19000,19067,19154,19241,19303,19367,19430,19500,19606,19722,19819,19933,19993,21248,22148,22231,22308", "endLines": "25,56,57,58,59,60,68,69,70,92,93,163,168,171,173,174,175,176,177,178,179,180,181,182,183,184,185,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,254,267,268,269", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "1174,4270,4348,4433,4530,4623,5445,5575,5659,8177,8245,13878,14336,14549,14724,14784,14850,14906,14977,15037,15091,15217,15274,15336,15390,15465,15599,16421,16499,16594,16679,16760,16897,16981,17067,17200,17291,17369,17425,17480,17546,17620,17698,17769,17851,17923,18000,18080,18154,18261,18354,18427,18519,18615,18689,18765,18861,18913,18995,19062,19149,19236,19298,19362,19425,19495,19601,19717,19814,19928,19988,20047,21323,22226,22303,22378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ef18ad19ff26599d64ec0eff4ea7dc70\\transformed\\appcompat-1.7.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1179,1284,1379,1486,1572,1676,1795,1880,1962,2053,2146,2241,2335,2435,2528,2623,2718,2809,2900,2986,3090,3202,3303,3408,3522,3624,3793,22063", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "1279,1374,1481,1567,1671,1790,1875,1957,2048,2141,2236,2330,2430,2523,2618,2713,2804,2895,2981,3085,3197,3298,3403,3517,3619,3788,3885,22143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f9b6829f7f71dcf7ed8c3a43ef2febe5\\transformed\\jetified-exoplayer-ui-2.19.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "56,57", "startColumns": "4,4", "startOffsets": "3628,3693", "endColumns": "64,70", "endOffsets": "3688,3759"}, "to": {"startLines": "155,156", "startColumns": "4,4", "startOffsets": "13242,13307", "endColumns": "64,70", "endOffsets": "13302,13373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9eda951e918e947e7d9ddd1ba9b2c563\\transformed\\jetified-media3-session-1.4.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,234,308,401,498,606,695,784,885,973,1050,1148,1237,1331,1410,1516,1598,1692,1763,1837,1916,2005,2097", "endColumns": "74,103,73,92,96,107,88,88,100,87,76,97,88,93,78,105,81,93,70,73,78,88,91,96", "endOffsets": "125,229,303,396,493,601,690,779,880,968,1045,1143,1232,1326,1405,1511,1593,1687,1758,1832,1911,2000,2092,2189"}, "to": {"startLines": "91,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,186,187,188,189,190,191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8040,8250,8354,8428,8521,8618,8726,8815,8904,9005,9093,9170,9268,9357,9451,9530,9636,9718,15604,15675,15749,15828,15917,16009", "endColumns": "74,103,73,92,96,107,88,88,100,87,76,97,88,93,78,105,81,93,70,73,78,88,91,96", "endOffsets": "8110,8349,8423,8516,8613,8721,8810,8899,9000,9088,9165,9263,9352,9446,9525,9631,9713,9807,15670,15744,15823,15912,16004,16101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6a4b12df2937b548059e098326cd7bcc\\transformed\\jetified-play-services-wallet-18.1.3\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "73", "endOffsets": "275"}, "to": {"startLines": "283", "startColumns": "4", "startOffsets": "23439", "endColumns": "77", "endOffsets": "23512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8e1703580f38993d5096d25ba35ecdf1\\transformed\\jetified-media3-ui-1.4.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,295,585,864,945,1027,1107,1214,1321,1391,1458,1549,1641,1706,1777,1840,1912,2031,2155,2276,2344,2428,2499,2570,2674,2779,2846,2911,2964,3022,3070,3131,3205,3284,3360,3434,3498,3557,3628,3680,3743,3828,3913,3969", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,51,62,84,84,55,67", "endOffsets": "290,580,859,940,1022,1102,1209,1316,1386,1453,1544,1636,1701,1772,1835,1907,2026,2150,2271,2339,2423,2494,2565,2669,2774,2841,2906,2959,3017,3065,3126,3200,3279,3355,3429,3493,3552,3623,3675,3738,3823,3908,3964,4032"}, "to": {"startLines": "2,11,16,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,144,145,146,147,148,149,150,151,152,153,154,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,390,680,9812,9893,9975,10055,10162,10269,10339,10406,10497,10589,10654,10725,10788,10860,10979,11103,11224,11292,11376,11447,11518,11622,11727,11794,12525,12578,12636,12684,12745,12819,12898,12974,13048,13112,13171,13378,13430,13493,13578,13663,13719", "endLines": "10,15,20,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,144,145,146,147,148,149,150,151,152,153,154,157,158,159,160,161,162", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,51,62,84,84,55,67", "endOffsets": "385,675,954,9888,9970,10050,10157,10264,10334,10401,10492,10584,10649,10720,10783,10855,10974,11098,11219,11287,11371,11442,11513,11617,11722,11789,11854,12573,12631,12679,12740,12814,12893,12969,13043,13107,13166,13237,13425,13488,13573,13658,13714,13782"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,213,287,356,437,504,575,656,739,823,912,984,1070,1153,1229,1309,1391,1470,1548,1624,1714,1787,1866,1944", "endColumns": "73,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "124,208,282,351,432,499,570,651,734,818,907,979,1065,1148,1224,1304,1386,1465,1543,1619,1709,1782,1861,1939,2020"}, "to": {"startLines": "53,71,167,169,170,172,192,193,194,255,256,257,265,270,271,272,273,274,275,276,277,279,280,281,282", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3890,5664,14199,14341,14410,14554,16106,16177,16258,21328,21412,21501,21977,22383,22466,22542,22622,22704,22783,22861,22937,23128,23201,23280,23358", "endColumns": "73,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "3959,5743,14268,14405,14486,14616,16172,16253,16336,21407,21496,21568,22058,22461,22537,22617,22699,22778,22856,22932,23022,23196,23275,23353,23434"}}]}]}