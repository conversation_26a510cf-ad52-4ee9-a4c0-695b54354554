4com.swmansion.gesturehandler.RNGestureHandlerPackage5com.swmansion.gesturehandler.core.FlingGestureHandler=com.swmansion.gesturehandler.core.FlingGestureHandler.FactoryDcom.swmansion.gesturehandler.core.GestureHandler.AdaptEventException5com.swmansion.gesturehandler.core.HoverGestureHandler=com.swmansion.gesturehandler.core.HoverGestureHandler.Factory9com.swmansion.gesturehandler.core.LongPressGestureHandlerAcom.swmansion.gesturehandler.core.LongPressGestureHandler.Factory6com.swmansion.gesturehandler.core.ManualGestureHandler>com.swmansion.gesturehandler.core.ManualGestureHandler.Factory:com.swmansion.gesturehandler.core.NativeViewGestureHandlerBcom.swmansion.gesturehandler.core.NativeViewGestureHandler.FactoryGcom.swmansion.gesturehandler.core.NativeViewGestureHandler.TextViewHookGcom.swmansion.gesturehandler.core.NativeViewGestureHandler.EditTextHookQcom.swmansion.gesturehandler.core.NativeViewGestureHandler.SwipeRefreshLayoutHookIcom.swmansion.gesturehandler.core.NativeViewGestureHandler.ScrollViewHookMcom.swmansion.gesturehandler.core.NativeViewGestureHandler.ReactViewGroupHook3com.swmansion.gesturehandler.core.PanGestureHandler;com.swmansion.gesturehandler.core.PanGestureHandler.Factory5com.swmansion.gesturehandler.core.PinchGestureHandler=<EMAIL>;com.swmansion.gesturehandler.core.TapGestureHandler.FactoryDcom.swmansion.gesturehandler.react.RNGestureHandlerButtonViewManagerTcom.swmansion.gesturehandler.react.RNGestureHandlerButtonViewManager.ButtonViewGroupBcom.swmansion.gesturehandler.react.RNGestureHandlerEnabledRootView8com.swmansion.gesturehandler.react.RNGestureHandlerEventBcom.swmansion.gesturehandler.react.RNGestureHandlerEventDispatcherEcom.swmansion.gesturehandler.react.RNGestureHandlerInteractionManager9com.swmansion.gesturehandler.react.RNGestureHandlerModule;com.swmansion.gesturehandler.react.RNGestureHandlerRegistryTcom.swmansion.gesturehandler.react.RNGestureHandlerRootHelper.RootViewGestureHandler;com.swmansion.gesturehandler.react.RNGestureHandlerRootViewBcom.swmansion.gesturehandler.react.RNGestureHandlerRootViewManagerCcom.swmansion.gesturehandler.react.RNGestureHandlerStateChangeEvent=com.swmansion.gesturehandler.react.RNGestureHandlerTouchEvent<com.swmansion.gesturehandler.react.RNViewConfigurationHelperTcom.swmansion.gesturehandler.react.eventbuilders.FlingGestureHandlerEventDataBuilderTcom.swmansion.gesturehandler.react.eventbuilders.HoverGestureHandlerEventDataBuilderXcom.swmansion.gesturehandler.react.eventbuilders.LongPressGestureHandlerEventDataBuilderUcom.swmansion.gesturehandler.react.eventbuilders.ManualGestureHandlerEventDataBuilderUcom.swmansion.gesturehandler.react.eventbuilders.NativeGestureHandlerEventDataBuilderRcom.swmansion.gesturehandler.react.eventbuilders.PanGestureHandlerEventDataBuilderTcom.swmansion.gesturehandler.react.eventbuilders.PinchGestureHandlerEventDataBuilderWcom.swmansion.gesturehandler.react.eventbuilders.RotationGestureHandlerEventDataBuilderRcom.swmansion.gesturehandler.react.eventbuilders.TapGestureHandlerEventDataBuilder                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      