{"logs": [{"outputFile": "com.adtip.app.adtip_app-mergeDebugResources-4:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\91b5c4a59b6d6a7985119f29ba44ddae\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,259,328,403,467,564,658", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "120,185,254,323,398,462,559,653,726"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11291,11361,11426,11495,11564,11639,11703,11800,11894", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "11356,11421,11490,11559,11634,11698,11795,11889,11962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0cb78797bbebcc5583f5b3648f5d6ccd\\transformed\\browser-1.8.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,101", "endOffsets": "151,252,363,465"}, "to": {"startLines": "87,161,162,163", "startColumns": "4,4,4,4", "startOffsets": "7607,13235,13336,13447", "endColumns": "100,100,110,101", "endOffsets": "7703,13331,13442,13544"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6a4b12df2937b548059e098326cd7bcc\\transformed\\jetified-play-services-wallet-18.1.3\\res\\values-mr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "74", "endOffsets": "276"}, "to": {"startLines": "280", "startColumns": "4", "startOffsets": "22607", "endColumns": "78", "endOffsets": "22681"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,272,343,425,492,559,633,709,789,869,937,1020,1102,1177,1263,1350,1425,1496,1567,1658,1730,1805,1874", "endColumns": "68,78,68,70,81,66,66,73,75,79,79,67,82,81,74,85,86,74,70,70,90,71,74,68,72", "endOffsets": "119,198,267,338,420,487,554,628,704,784,864,932,1015,1097,1172,1258,1345,1420,1491,1562,1653,1725,1800,1869,1942"}, "to": {"startLines": "50,68,164,166,167,169,189,190,191,252,253,254,262,267,268,269,270,271,272,273,274,276,277,278,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3615,5369,13549,13683,13754,13895,15358,15425,15499,20544,20624,20704,21181,21579,21661,21736,21822,21909,21984,22055,22126,22318,22390,22465,22534", "endColumns": "68,78,68,70,81,66,66,73,75,79,79,67,82,81,74,85,86,74,70,70,90,71,74,68,72", "endOffsets": "3679,5443,13613,13749,13831,13957,15420,15494,15570,20619,20699,20767,21259,21656,21731,21817,21904,21979,22050,22121,22212,22385,22460,22529,22602"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6ee598c7bab1dd2859d5d01f2229eaf5\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-mr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "77", "startColumns": "4", "startOffsets": "6429", "endColumns": "142", "endOffsets": "6567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9eda951e918e947e7d9ddd1ba9b2c563\\transformed\\jetified-media3-session-1.4.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,232,306,407,502,593,679,763,861,937,999,1092,1170,1264,1341,1435,1509,1594,1663,1733,1807,1884,1969", "endColumns": "78,97,73,100,94,90,85,83,97,75,61,92,77,93,76,93,73,84,68,69,73,76,84,89", "endOffsets": "129,227,301,402,497,588,674,758,856,932,994,1087,1165,1259,1336,1430,1504,1589,1658,1728,1802,1879,1964,2054"}, "to": {"startLines": "88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,183,184,185,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7708,7907,8005,8079,8180,8275,8366,8452,8536,8634,8710,8772,8865,8943,9037,9114,9208,9282,14893,14962,15032,15106,15183,15268", "endColumns": "78,97,73,100,94,90,85,83,97,75,61,92,77,93,76,93,73,84,68,69,73,76,84,89", "endOffsets": "7782,8000,8074,8175,8270,8361,8447,8531,8629,8705,8767,8860,8938,9032,9109,9203,9277,9362,14957,15027,15101,15178,15263,15353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd11809084d63593099820b405d61701\\transformed\\material-1.12.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,344,431,514,607,691,791,907,989,1046,1109,1200,1265,1324,1412,1474,1536,1596,1663,1726,1780,1894,1951,2012,2066,2136,2255,2336,2413,2502,2584,2669,2804,2881,2958,3099,3185,3269,3325,3377,3443,3513,3591,3662,3744,3814,3890,3961,4030,4144,4240,4314,4412,4508,4582,4652,4754,4809,4897,4964,5051,5144,5207,5271,5334,5400,5500,5609,5703,5810,5870,5926,6004,6088,6166", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,84,86,82,92,83,99,115,81,56,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,76,88,81,84,134,76,76,140,85,83,55,51,65,69,77,70,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77,83,77,72", "endOffsets": "254,339,426,509,602,686,786,902,984,1041,1104,1195,1260,1319,1407,1469,1531,1591,1658,1721,1775,1889,1946,2007,2061,2131,2250,2331,2408,2497,2579,2664,2799,2876,2953,3094,3180,3264,3320,3372,3438,3508,3586,3657,3739,3809,3885,3956,4025,4139,4235,4309,4407,4503,4577,4647,4749,4804,4892,4959,5046,5139,5202,5266,5329,5395,5495,5604,5698,5805,5865,5921,5999,6083,6161,6234"}, "to": {"startLines": "19,53,54,55,56,57,65,66,67,89,90,160,165,168,170,171,172,173,174,175,176,177,178,179,180,181,182,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,251,264,265,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,3907,3992,4079,4162,4255,5071,5171,5287,7787,7844,13144,13618,13836,13962,14050,14112,14174,14234,14301,14364,14418,14532,14589,14650,14704,14774,15575,15656,15733,15822,15904,15989,16124,16201,16278,16419,16505,16589,16645,16697,16763,16833,16911,16982,17064,17134,17210,17281,17350,17464,17560,17634,17732,17828,17902,17972,18074,18129,18217,18284,18371,18464,18527,18591,18654,18720,18820,18929,19023,19130,19190,20466,21344,21428,21506", "endLines": "22,53,54,55,56,57,65,66,67,89,90,160,165,168,170,171,172,173,174,175,176,177,178,179,180,181,182,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,251,264,265,266", "endColumns": "12,84,86,82,92,83,99,115,81,56,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,76,88,81,84,134,76,76,140,85,83,55,51,65,69,77,70,81,69,75,70,68,113,95,73,97,95,73,69,101,54,87,66,86,92,62,63,62,65,99,108,93,106,59,55,77,83,77,72", "endOffsets": "920,3987,4074,4157,4250,4334,5166,5282,5364,7839,7902,13230,13678,13890,14045,14107,14169,14229,14296,14359,14413,14527,14584,14645,14699,14769,14888,15651,15728,15817,15899,15984,16119,16196,16273,16414,16500,16584,16640,16692,16758,16828,16906,16977,17059,17129,17205,17276,17345,17459,17555,17629,17727,17823,17897,17967,18069,18124,18212,18279,18366,18459,18522,18586,18649,18715,18815,18924,19018,19125,19185,19241,20539,21423,21501,21574"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfbd6dfbd7eb9930845c16c988ee8525\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,111", "endOffsets": "161,273"}, "to": {"startLines": "51,52", "startColumns": "4,4", "startOffsets": "3684,3795", "endColumns": "110,111", "endOffsets": "3790,3902"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ef18ad19ff26599d64ec0eff4ea7dc70\\transformed\\appcompat-1.7.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,263", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1036,1142,1249,1339,1440,1552,1630,1707,1798,1891,1984,2081,2181,2274,2369,2463,2554,2645,2725,2832,2933,3030,3139,3241,3355,3512,21264", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "1031,1137,1244,1334,1435,1547,1625,1702,1793,1886,1979,2076,2176,2269,2364,2458,2549,2640,2720,2827,2928,3025,3134,3236,3350,3507,3610,21339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e4f7aaf1dd3251b595a6d29fbf072499\\transformed\\core-1.16.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "58,59,60,61,62,63,64,275", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4339,4439,4543,4644,4747,4849,4954,22217", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "4434,4538,4639,4742,4844,4949,5066,22313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c9fc1356237c73a36541687fd82ee2b5\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-mr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,296,352,421,493,604,666,799,924,1054,1105,1162,1272,1363,1405,1494,1530,1567,1619,1703,1744", "endColumns": "45,50,55,68,71,110,61,132,124,129,50,56,109,90,41,88,35,36,51,83,40,55", "endOffsets": "244,295,351,420,492,603,665,798,923,1053,1104,1161,1271,1362,1404,1493,1529,1566,1618,1702,1743,1799"}, "to": {"startLines": "237,238,239,240,241,242,243,244,245,246,247,248,249,250,255,256,257,258,259,260,261,281", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19246,19296,19351,19411,19484,19560,19675,19741,19878,20007,20141,20196,20257,20371,20772,20818,20911,20951,20992,21048,21136,22686", "endColumns": "49,54,59,72,75,114,65,136,128,133,54,60,113,94,45,92,39,40,55,87,44,59", "endOffsets": "19291,19346,19406,19479,19555,19670,19736,19873,20002,20136,20191,20252,20366,20461,20813,20906,20946,20987,21043,21131,21176,22741"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f9b6829f7f71dcf7ed8c3a43ef2febe5\\transformed\\jetified-exoplayer-ui-2.19.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3260,3325", "endColumns": "64,67", "endOffsets": "3320,3388"}, "to": {"startLines": "152,153", "startColumns": "4,4", "startOffsets": "12632,12697", "endColumns": "64,67", "endOffsets": "12692,12760"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\31cc88bd1a811e01477d23dfca8d3955\\transformed\\jetified-play-services-base-18.5.0\\res\\values-mr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,460,579,687,828,945,1049,1142,1288,1392,1542,1662,1797,1946,2002,2064", "endColumns": "102,163,118,107,140,116,103,92,145,103,149,119,134,148,55,61,76", "endOffsets": "295,459,578,686,827,944,1048,1141,1287,1391,1541,1661,1796,1945,2001,2063,2140"}, "to": {"startLines": "69,70,71,72,73,74,75,76,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5448,5555,5723,5846,5958,6103,6224,6332,6572,6722,6830,6984,7108,7247,7400,7460,7526", "endColumns": "106,167,122,111,144,120,107,96,149,107,153,123,138,152,59,65,80", "endOffsets": "5550,5718,5841,5953,6098,6219,6327,6424,6717,6825,6979,7103,7242,7395,7455,7521,7602"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8e1703580f38993d5096d25ba35ecdf1\\transformed\\jetified-media3-ui-1.4.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,487,671,753,833,916,1003,1097,1165,1229,1319,1410,1475,1543,1603,1671,1784,1903,2014,2086,2165,2236,2306,2388,2468,2532,2595,2648,2706,2754,2815,2876,2943,3005,3071,3130,3195,3260,3313,3373,3447,3521,3574", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,112,118,110,71,78,70,69,81,79,63,62,52,57,47,60,60,66,61,65,58,64,64,52,59,73,73,52,64", "endOffsets": "281,482,666,748,828,911,998,1092,1160,1224,1314,1405,1470,1538,1598,1666,1779,1898,2009,2081,2160,2231,2301,2383,2463,2527,2590,2643,2701,2749,2810,2871,2938,3000,3066,3125,3190,3255,3308,3368,3442,3516,3569,3634"}, "to": {"startLines": "2,11,15,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,154,155,156,157,158,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,582,9367,9449,9529,9612,9699,9793,9861,9925,10015,10106,10171,10239,10299,10367,10480,10599,10710,10782,10861,10932,11002,11084,11164,11228,11967,12020,12078,12126,12187,12248,12315,12377,12443,12502,12567,12765,12818,12878,12952,13026,13079", "endLines": "10,14,18,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,154,155,156,157,158,159", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,112,118,110,71,78,70,69,81,79,63,62,52,57,47,60,60,66,61,65,58,64,64,52,59,73,73,52,64", "endOffsets": "376,577,761,9444,9524,9607,9694,9788,9856,9920,10010,10101,10166,10234,10294,10362,10475,10594,10705,10777,10856,10927,10997,11079,11159,11223,11286,12015,12073,12121,12182,12243,12310,12372,12438,12497,12562,12627,12813,12873,12947,13021,13074,13139"}}]}]}