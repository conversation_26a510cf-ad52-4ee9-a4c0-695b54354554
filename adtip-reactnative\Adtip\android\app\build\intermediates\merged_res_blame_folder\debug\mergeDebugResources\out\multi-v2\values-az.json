{"logs": [{"outputFile": "com.adtip.app.adtip_app-mergeDebugResources-4:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6a4b12df2937b548059e098326cd7bcc\\transformed\\jetified-play-services-wallet-18.1.3\\res\\values-az\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "70", "endOffsets": "272"}, "to": {"startLines": "262", "startColumns": "4", "startOffsets": "21371", "endColumns": "74", "endOffsets": "21441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfbd6dfbd7eb9930845c16c988ee8525\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,117", "endOffsets": "159,277"}, "to": {"startLines": "50,51", "startColumns": "4,4", "startOffsets": "3628,3737", "endColumns": "108,117", "endOffsets": "3732,3850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6ee598c7bab1dd2859d5d01f2229eaf5\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-az\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "157", "endOffsets": "352"}, "to": {"startLines": "76", "startColumns": "4", "startOffsets": "6384", "endColumns": "161", "endOffsets": "6541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8e1703580f38993d5096d25ba35ecdf1\\transformed\\jetified-media3-ui-1.4.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,476,660,748,837,914,1006,1094,1170,1234,1325,1416,1481,1546,1608,1676,1804,1936,2062,2133,2214,2284,2360,2456,2553,2622,2688,2741,2799,2847,2908,2972,3044,3103,3166,3229,3289,3355,3407,3465,3537,3609,3663", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,87,88,76,91,87,75,63,90,90,64,64,61,67,127,131,125,70,80,69,75,95,96,68,65,52,57,47,60,63,71,58,62,62,59,65,51,57,71,71,53,65", "endOffsets": "280,471,655,743,832,909,1001,1089,1165,1229,1320,1411,1476,1541,1603,1671,1799,1931,2057,2128,2209,2279,2355,2451,2548,2617,2683,2736,2794,2842,2903,2967,3039,3098,3161,3224,3284,3350,3402,3460,3532,3604,3658,3724"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,571,9403,9491,9580,9657,9749,9837,9913,9977,10068,10159,10224,10289,10351,10419,10547,10679,10805,10876,10957,11027,11103,11199,11296,11365,12088,12141,12199,12247,12308,12372,12444,12503,12566,12629,12689,12885,12937,12995,13067,13139,13193", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,153,154,155,156,157,158", "endColumns": "17,12,12,87,88,76,91,87,75,63,90,90,64,64,61,67,127,131,125,70,80,69,75,95,96,68,65,52,57,47,60,63,71,58,62,62,59,65,51,57,71,71,53,65", "endOffsets": "375,566,750,9486,9575,9652,9744,9832,9908,9972,10063,10154,10219,10284,10346,10414,10542,10674,10800,10871,10952,11022,11098,11194,11291,11360,11426,12136,12194,12242,12303,12367,12439,12498,12561,12624,12684,12750,12932,12990,13062,13134,13188,13254"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e4f7aaf1dd3251b595a6d29fbf072499\\transformed\\core-1.16.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "57,58,59,60,61,62,63,261", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4272,4373,4475,4578,4682,4783,4888,21270", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "4368,4470,4573,4677,4778,4883,4994,21366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\91b5c4a59b6d6a7985119f29ba44ddae\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,186,249,314,392,459,548,641", "endColumns": "69,60,62,64,77,66,88,92,70", "endOffsets": "120,181,244,309,387,454,543,636,707"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11431,11501,11562,11625,11690,11768,11835,11924,12017", "endColumns": "69,60,62,64,77,66,88,92,70", "endOffsets": "11496,11557,11620,11685,11763,11830,11919,12012,12083"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c9fc1356237c73a36541687fd82ee2b5\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-az\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,240,287,342,411,483,591,654,783,884,1010,1060,1122,1228,1310,1358,1450,1488,1525,1575,1655,1695", "endColumns": "40,46,54,68,71,107,62,128,100,125,49,61,105,81,47,91,37,36,49,79,39,55", "endOffsets": "239,286,341,410,482,590,653,782,883,1009,1059,1121,1227,1309,1357,1449,1487,1524,1574,1654,1694,1750"}, "to": {"startLines": "233,234,235,236,237,238,239,240,241,242,243,244,245,246,248,249,250,251,252,253,254,263", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19140,19185,19236,19295,19368,19444,19556,19623,19756,19861,19991,20045,20111,20221,20387,20439,20535,20577,20618,20672,20756,21446", "endColumns": "44,50,58,72,75,111,66,132,104,129,53,65,109,85,51,95,41,40,53,83,43,59", "endOffsets": "19180,19231,19290,19363,19439,19551,19618,19751,19856,19986,20040,20106,20216,20302,20434,20530,20572,20613,20667,20751,20795,21501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0cb78797bbebcc5583f5b3648f5d6ccd\\transformed\\browser-1.8.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,255,363", "endColumns": "95,103,107,102", "endOffsets": "146,250,358,461"}, "to": {"startLines": "86,160,161,162", "startColumns": "4,4,4,4", "startOffsets": "7618,13353,13457,13565", "endColumns": "95,103,107,102", "endOffsets": "7709,13452,13560,13663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ef18ad19ff26599d64ec0eff4ea7dc70\\transformed\\appcompat-1.7.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "920,1030,1131,1241,1329,1436,1550,1632,1710,1801,1894,1988,2087,2187,2280,2375,2469,2560,2652,2737,2842,2948,3048,3157,3262,3364,3522,20800", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "1025,1126,1236,1324,1431,1545,1627,1705,1796,1889,1983,2082,2182,2275,2370,2464,2555,2647,2732,2837,2943,3043,3152,3257,3359,3517,3623,20879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f9b6829f7f71dcf7ed8c3a43ef2febe5\\transformed\\jetified-exoplayer-ui-2.19.1\\res\\values-az\\values-az.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3355,3419", "endColumns": "63,65", "endOffsets": "3414,3480"}, "to": {"startLines": "151,152", "startColumns": "4,4", "startOffsets": "12755,12819", "endColumns": "63,65", "endOffsets": "12814,12880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd11809084d63593099820b405d61701\\transformed\\material-1.12.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,353,438,518,603,682,778,894,974,1035,1099,1193,1261,1320,1415,1478,1542,1601,1668,1731,1785,1900,1958,2020,2074,2145,2277,2361,2440,2532,2616,2696,2830,2906,2982,3111,3195,3274,3331,3382,3448,3518,3596,3667,3747,3817,3893,3971,4042,4140,4226,4309,4402,4495,4568,4640,4734,4788,4872,4939,5023,5111,5175,5240,5304,5374,5476,5580,5676,5777,5838,5893,5973,6060,6135", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,87,84,79,84,78,95,115,79,60,63,93,67,58,94,62,63,58,66,62,53,114,57,61,53,70,131,83,78,91,83,79,133,75,75,128,83,78,56,50,65,69,77,70,79,69,75,77,70,97,85,82,92,92,72,71,93,53,83,66,83,87,63,64,63,69,101,103,95,100,60,54,79,86,74,73", "endOffsets": "260,348,433,513,598,677,773,889,969,1030,1094,1188,1256,1315,1410,1473,1537,1596,1663,1726,1780,1895,1953,2015,2069,2140,2272,2356,2435,2527,2611,2691,2825,2901,2977,3106,3190,3269,3326,3377,3443,3513,3591,3662,3742,3812,3888,3966,4037,4135,4221,4304,4397,4490,4563,4635,4729,4783,4867,4934,5018,5106,5170,5235,5299,5369,5471,5575,5671,5772,5833,5888,5968,6055,6130,6204"}, "to": {"startLines": "19,52,53,54,55,56,64,65,66,88,89,159,163,166,168,169,170,171,172,173,174,175,176,177,178,179,180,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,247,256,257,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "755,3855,3943,4028,4108,4193,4999,5095,5211,7790,7851,13259,13668,13887,14014,14109,14172,14236,14295,14362,14425,14479,14594,14652,14714,14768,14839,15524,15608,15687,15779,15863,15943,16077,16153,16229,16358,16442,16521,16578,16629,16695,16765,16843,16914,16994,17064,17140,17218,17289,17387,17473,17556,17649,17742,17815,17887,17981,18035,18119,18186,18270,18358,18422,18487,18551,18621,18723,18827,18923,19024,19085,20307,20884,20971,21046", "endLines": "22,52,53,54,55,56,64,65,66,88,89,159,163,166,168,169,170,171,172,173,174,175,176,177,178,179,180,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,247,256,257,258", "endColumns": "12,87,84,79,84,78,95,115,79,60,63,93,67,58,94,62,63,58,66,62,53,114,57,61,53,70,131,83,78,91,83,79,133,75,75,128,83,78,56,50,65,69,77,70,79,69,75,77,70,97,85,82,92,92,72,71,93,53,83,66,83,87,63,64,63,69,101,103,95,100,60,54,79,86,74,73", "endOffsets": "915,3938,4023,4103,4188,4267,5090,5206,5286,7846,7910,13348,13731,13941,14104,14167,14231,14290,14357,14420,14474,14589,14647,14709,14763,14834,14966,15603,15682,15774,15858,15938,16072,16148,16224,16353,16437,16516,16573,16624,16690,16760,16838,16909,16989,17059,17135,17213,17284,17382,17468,17551,17644,17737,17810,17882,17976,18030,18114,18181,18265,18353,18417,18482,18546,18616,18718,18822,18918,19019,19080,19135,20382,20966,21041,21115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,136,205,287,355,423,498", "endColumns": "80,68,81,67,67,74,74", "endOffsets": "131,200,282,350,418,493,568"}, "to": {"startLines": "67,164,165,167,187,259,260", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5291,13736,13805,13946,15456,21120,21195", "endColumns": "80,68,81,67,67,74,74", "endOffsets": "5367,13800,13882,14009,15519,21190,21265"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\31cc88bd1a811e01477d23dfca8d3955\\transformed\\jetified-play-services-base-18.5.0\\res\\values-az\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,449,578,684,826,955,1071,1173,1335,1441,1586,1719,1859,2011,2071,2132", "endColumns": "104,150,128,105,141,128,115,101,161,105,144,132,139,151,59,60,76", "endOffsets": "297,448,577,683,825,954,1070,1172,1334,1440,1585,1718,1858,2010,2070,2131,2208"}, "to": {"startLines": "68,69,70,71,72,73,74,75,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5372,5481,5636,5769,5879,6025,6158,6278,6546,6712,6822,6971,7108,7252,7408,7472,7537", "endColumns": "108,154,132,109,145,132,119,105,165,109,148,136,143,155,63,64,80", "endOffsets": "5476,5631,5764,5874,6020,6153,6273,6379,6707,6817,6966,7103,7247,7403,7467,7532,7613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9eda951e918e947e7d9ddd1ba9b2c563\\transformed\\jetified-media3-session-1.4.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,238,312,405,503,584,664,748,848,924,992,1104,1181,1275,1353,1447,1526,1619,1690,1758,1835,1916,2008", "endColumns": "75,106,73,92,97,80,79,83,99,75,67,111,76,93,77,93,78,92,70,67,76,80,91,95", "endOffsets": "126,233,307,400,498,579,659,743,843,919,987,1099,1176,1270,1348,1442,1521,1614,1685,1753,1830,1911,2003,2099"}, "to": {"startLines": "87,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,181,182,183,184,185,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7714,7915,8022,8096,8189,8287,8368,8448,8532,8632,8708,8776,8888,8965,9059,9137,9231,9310,14971,15042,15110,15187,15268,15360", "endColumns": "75,106,73,92,97,80,79,83,99,75,67,111,76,93,77,93,78,92,70,67,76,80,91,95", "endOffsets": "7785,8017,8091,8184,8282,8363,8443,8527,8627,8703,8771,8883,8960,9054,9132,9226,9305,9398,15037,15105,15182,15263,15355,15451"}}]}]}