{"logs": [{"outputFile": "com.adtip.app.adtip_app-mergeDebugResources-4:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ef18ad19ff26599d64ec0eff4ea7dc70\\transformed\\appcompat-1.7.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2248,2353,2467,2570,2739,2835", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2243,2348,2462,2565,2734,2830,2917"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1157,1278,1375,1482,1568,1672,1794,1879,1961,2052,2145,2240,2334,2434,2527,2622,2717,2808,2899,2987,3090,3194,3300,3405,3519,3622,3791,21033", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "1273,1370,1477,1563,1667,1789,1874,1956,2047,2140,2235,2329,2429,2522,2617,2712,2803,2894,2982,3085,3189,3295,3400,3514,3617,3786,3882,21115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e4f7aaf1dd3251b595a6d29fbf072499\\transformed\\core-1.16.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "60,61,62,63,64,65,66,257", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4557,4655,4757,4855,4959,5063,5165,21359", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "4650,4752,4850,4954,5058,5160,5277,21455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f9b6829f7f71dcf7ed8c3a43ef2febe5\\transformed\\jetified-exoplayer-ui-2.19.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "56,57", "startColumns": "4,4", "startOffsets": "3574,3639", "endColumns": "64,70", "endOffsets": "3634,3705"}, "to": {"startLines": "153,154", "startColumns": "4,4", "startOffsets": "13117,13182", "endColumns": "64,70", "endOffsets": "13177,13248"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd11809084d63593099820b405d61701\\transformed\\material-1.12.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,327,406,486,568,670,764,860,986,1067,1129,1195,1287,1364,1427,1535,1595,1661,1717,1788,1848,1902,2021,2078,2140,2194,2269,2393,2481,2558,2652,2736,2819,2964,3049,3135,3268,3356,3434,3488,3542,3608,3682,3760,3831,3913,3985,4062,4135,4205,4314,4407,4479,4571,4667,4741,4817,4913,4966,5048,5115,5202,5289,5351,5415,5478,5547,5655,5760,5861,5964,6022,6080,6160,6246,6322", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,78,79,81,101,93,95,125,80,61,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,76,93,83,82,144,84,85,132,87,77,53,53,65,73,77,70,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79,85,75,76", "endOffsets": "322,401,481,563,665,759,855,981,1062,1124,1190,1282,1359,1422,1530,1590,1656,1712,1783,1843,1897,2016,2073,2135,2189,2264,2388,2476,2553,2647,2731,2814,2959,3044,3130,3263,3351,3429,3483,3537,3603,3677,3755,3826,3908,3980,4057,4130,4200,4309,4402,4474,4566,4662,4736,4812,4908,4961,5043,5110,5197,5284,5346,5410,5473,5542,5650,5755,5856,5959,6017,6075,6155,6241,6317,6394"}, "to": {"startLines": "21,55,56,57,58,59,67,68,69,90,91,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,245,254,255,256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "930,4120,4199,4279,4361,4463,5282,5378,5504,7968,8030,13658,14064,14141,14204,14312,14372,14438,14494,14565,14625,14679,14798,14855,14917,14971,15046,15690,15778,15855,15949,16033,16116,16261,16346,16432,16565,16653,16731,16785,16839,16905,16979,17057,17128,17210,17282,17359,17432,17502,17611,17704,17776,17868,17964,18038,18114,18210,18263,18345,18412,18499,18586,18648,18712,18775,18844,18952,19057,19158,19261,19319,20542,21120,21206,21282", "endLines": "25,55,56,57,58,59,67,68,69,90,91,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,245,254,255,256", "endColumns": "12,78,79,81,101,93,95,125,80,61,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,76,93,83,82,144,84,85,132,87,77,53,53,65,73,77,70,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79,85,75,76", "endOffsets": "1152,4194,4274,4356,4458,4552,5373,5499,5580,8025,8091,13745,14136,14199,14307,14367,14433,14489,14560,14620,14674,14793,14850,14912,14966,15041,15165,15773,15850,15944,16028,16111,16256,16341,16427,16560,16648,16726,16780,16834,16900,16974,17052,17123,17205,17277,17354,17427,17497,17606,17699,17771,17863,17959,18033,18109,18205,18258,18340,18407,18494,18581,18643,18707,18770,18839,18947,19052,19153,19256,19314,19372,20617,21201,21277,21354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9eda951e918e947e7d9ddd1ba9b2c563\\transformed\\jetified-media3-session-1.4.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,141,247,321,425,522,625,725,813,914,1012,1092,1197,1297,1391,1470,1566,1648,1743,1816,1892,1974,2062,2160", "endColumns": "85,105,73,103,96,102,99,87,100,97,79,104,99,93,78,95,81,94,72,75,81,87,97,102", "endOffsets": "136,242,316,420,517,620,720,808,909,1007,1087,1192,1292,1386,1465,1561,1643,1738,1811,1887,1969,2057,2155,2258"}, "to": {"startLines": "89,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,180,181,182,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7882,8096,8202,8276,8380,8477,8580,8680,8768,8869,8967,9047,9152,9252,9346,9425,9521,9603,15170,15243,15319,15401,15489,15587", "endColumns": "85,105,73,103,96,102,99,87,100,97,79,104,99,93,78,95,81,94,72,75,81,87,97,102", "endOffsets": "7963,8197,8271,8375,8472,8575,8675,8763,8864,8962,9042,9147,9247,9341,9420,9516,9598,9693,15238,15314,15396,15484,15582,15685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6ee598c7bab1dd2859d5d01f2229eaf5\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-bs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "136", "endOffsets": "331"}, "to": {"startLines": "78", "startColumns": "4", "startOffsets": "6577", "endColumns": "140", "endOffsets": "6713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c9fc1356237c73a36541687fd82ee2b5\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-bs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,288,344,409,482,585,648,787,882,998,1051,1108,1221,1308,1349,1438,1475,1508,1566,1651,1691", "endColumns": "41,46,55,64,72,102,62,138,94,115,52,56,112,86,40,88,36,32,57,84,39,55", "endOffsets": "240,287,343,408,481,584,647,786,881,997,1050,1107,1220,1307,1348,1437,1474,1507,1565,1650,1690,1746"}, "to": {"startLines": "231,232,233,234,235,236,237,238,239,240,241,242,243,244,246,247,248,249,250,251,252,259", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19377,19423,19474,19534,19603,19680,19787,19854,19997,20096,20216,20273,20334,20451,20622,20667,20760,20801,20838,20900,20989,21538", "endColumns": "45,50,59,68,76,106,66,142,98,119,56,60,116,90,44,92,40,36,61,88,43,59", "endOffsets": "19418,19469,19529,19598,19675,19782,19849,19992,20091,20211,20268,20329,20446,20537,20662,20755,20796,20833,20895,20984,21028,21593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6a4b12df2937b548059e098326cd7bcc\\transformed\\jetified-play-services-wallet-18.1.3\\res\\values-bs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "73", "endOffsets": "275"}, "to": {"startLines": "258", "startColumns": "4", "startOffsets": "21460", "endColumns": "77", "endOffsets": "21533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\91b5c4a59b6d6a7985119f29ba44ddae\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,581,662", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "125,186,251,324,403,476,576,657,730"}, "to": {"startLines": "133,134,135,136,137,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11755,11830,11891,11956,12029,12108,12181,12281,12362", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "11825,11886,11951,12024,12103,12176,12276,12357,12430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfbd6dfbd7eb9930845c16c988ee8525\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,121", "endOffsets": "161,283"}, "to": {"startLines": "53,54", "startColumns": "4,4", "startOffsets": "3887,3998", "endColumns": "110,121", "endOffsets": "3993,4115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\31cc88bd1a811e01477d23dfca8d3955\\transformed\\jetified-play-services-base-18.5.0\\res\\values-bs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,573,679,825,947,1055,1153,1303,1406,1563,1686,1832,1974,2038,2096", "endColumns": "101,155,121,105,145,121,107,97,149,102,156,122,145,141,63,57,80", "endOffsets": "294,450,572,678,824,946,1054,1152,1302,1405,1562,1685,1831,1973,2037,2095,2176"}, "to": {"startLines": "70,71,72,73,74,75,76,77,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5585,5691,5851,5977,6087,6237,6363,6475,6718,6872,6979,7140,7267,7417,7563,7631,7693", "endColumns": "105,159,125,109,149,125,111,101,153,106,160,126,149,145,67,61,84", "endOffsets": "5686,5846,5972,6082,6232,6358,6470,6572,6867,6974,7135,7262,7412,7558,7626,7688,7773"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0cb78797bbebcc5583f5b3648f5d6ccd\\transformed\\browser-1.8.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,259,373", "endColumns": "103,99,113,99", "endOffsets": "154,254,368,468"}, "to": {"startLines": "88,162,163,164", "startColumns": "4,4,4,4", "startOffsets": "7778,13750,13850,13964", "endColumns": "103,99,113,99", "endOffsets": "7877,13845,13959,14059"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8e1703580f38993d5096d25ba35ecdf1\\transformed\\jetified-media3-ui-1.4.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,571,835,921,1008,1096,1194,1301,1371,1438,1534,1626,1691,1764,1827,1895,2009,2125,2241,2321,2405,2476,2547,2648,2750,2822,2892,2945,3003,3051,3112,3184,3251,3315,3386,3450,3509,3574,3626,3693,3774,3855,3911", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,113,115,115,79,83,70,70,100,101,71,69,52,57,47,60,71,66,63,70,63,58,64,51,66,80,80,55,67", "endOffsets": "288,566,830,916,1003,1091,1189,1296,1366,1433,1529,1621,1686,1759,1822,1890,2004,2120,2236,2316,2400,2471,2542,2643,2745,2817,2887,2940,2998,3046,3107,3179,3246,3310,3381,3445,3504,3569,3621,3688,3769,3850,3906,3974"}, "to": {"startLines": "2,11,16,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,142,143,144,145,146,147,148,149,150,151,152,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,388,666,9698,9784,9871,9959,10057,10164,10234,10301,10397,10489,10554,10627,10690,10758,10872,10988,11104,11184,11268,11339,11410,11511,11613,11685,12435,12488,12546,12594,12655,12727,12794,12858,12929,12993,13052,13253,13305,13372,13453,13534,13590", "endLines": "10,15,20,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,142,143,144,145,146,147,148,149,150,151,152,155,156,157,158,159,160", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,113,115,115,79,83,70,70,100,101,71,69,52,57,47,60,71,66,63,70,63,58,64,51,66,80,80,55,67", "endOffsets": "383,661,925,9779,9866,9954,10052,10159,10229,10296,10392,10484,10549,10622,10685,10753,10867,10983,11099,11179,11263,11334,11405,11506,11608,11680,11750,12483,12541,12589,12650,12722,12789,12853,12924,12988,13047,13112,13300,13367,13448,13529,13585,13653"}}]}]}