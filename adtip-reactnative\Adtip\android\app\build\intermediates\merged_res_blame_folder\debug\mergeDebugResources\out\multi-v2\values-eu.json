{"logs": [{"outputFile": "com.adtip.app.adtip_app-mergeDebugResources-4:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ef18ad19ff26599d64ec0eff4ea7dc70\\transformed\\appcompat-1.7.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "927,1036,1134,1244,1330,1436,1560,1646,1727,1819,1913,2009,2103,2204,2298,2394,2491,2583,2676,2758,2867,2976,3075,3184,3291,3402,3573,21081", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "1031,1129,1239,1325,1431,1555,1641,1722,1814,1908,2004,2098,2199,2293,2389,2486,2578,2671,2753,2862,2971,3070,3179,3286,3397,3568,3667,21159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f9b6829f7f71dcf7ed8c3a43ef2febe5\\transformed\\jetified-exoplayer-ui-2.19.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3436,3502", "endColumns": "65,66", "endOffsets": "3497,3564"}, "to": {"startLines": "150,151", "startColumns": "4,4", "startOffsets": "13112,13178", "endColumns": "65,66", "endOffsets": "13173,13240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9eda951e918e947e7d9ddd1ba9b2c563\\transformed\\jetified-media3-session-1.4.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,239,325,433,543,638,722,811,913,1004,1096,1207,1302,1399,1477,1574,1657,1751,1821,1896,1971,2050,2144", "endColumns": "83,99,85,107,109,94,83,88,101,90,91,110,94,96,77,96,82,93,69,74,74,78,93,96", "endOffsets": "134,234,320,428,538,633,717,806,908,999,1091,1202,1297,1394,1472,1569,1652,1746,1816,1891,1966,2045,2139,2236"}, "to": {"startLines": "86,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,177,178,179,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7806,8023,8123,8209,8317,8427,8522,8606,8695,8797,8888,8980,9091,9186,9283,9361,9458,9541,15153,15223,15298,15373,15452,15546", "endColumns": "83,99,85,107,109,94,83,88,101,90,91,110,94,96,77,96,82,93,69,74,74,78,93,96", "endOffsets": "7885,8118,8204,8312,8422,8517,8601,8690,8792,8883,8975,9086,9181,9278,9356,9453,9536,9630,15218,15293,15368,15447,15541,15638"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd11809084d63593099820b405d61701\\transformed\\material-1.12.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,382,487,567,674,774,872,987,1070,1136,1203,1302,1370,1431,1519,1582,1648,1712,1783,1846,1900,2009,2068,2131,2185,2259,2384,2474,2552,2641,2724,2804,2949,3032,3114,3252,3343,3426,3478,3531,3597,3668,3748,3819,3899,3977,4055,4128,4203,4310,4397,4484,4575,4668,4740,4816,4908,4959,5041,5107,5191,5277,5339,5403,5466,5534,5641,5750,5846,5951,6007,6064,6147,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,106,104,79,106,99,97,114,82,65,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,77,88,82,79,144,82,81,137,90,82,51,52,65,70,79,70,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82,84,76,76", "endOffsets": "270,377,482,562,669,769,867,982,1065,1131,1198,1297,1365,1426,1514,1577,1643,1707,1778,1841,1895,2004,2063,2126,2180,2254,2379,2469,2547,2636,2719,2799,2944,3027,3109,3247,3338,3421,3473,3526,3592,3663,3743,3814,3894,3972,4050,4123,4198,4305,4392,4479,4570,4663,4735,4811,4903,4954,5036,5102,5186,5272,5334,5398,5461,5529,5636,5745,5841,5946,6002,6059,6142,6227,6304,6381"}, "to": {"startLines": "19,52,53,54,55,56,64,65,66,87,88,158,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,242,251,252,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "752,3905,4012,4117,4197,4304,5135,5233,5348,7890,7956,13652,14071,14139,14200,14288,14351,14417,14481,14552,14615,14669,14778,14837,14900,14954,15028,15643,15733,15811,15900,15983,16063,16208,16291,16373,16511,16602,16685,16737,16790,16856,16927,17007,17078,17158,17236,17314,17387,17462,17569,17656,17743,17834,17927,17999,18075,18167,18218,18300,18366,18450,18536,18598,18662,18725,18793,18900,19009,19105,19210,19266,20574,21164,21249,21326", "endLines": "22,52,53,54,55,56,64,65,66,87,88,158,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,242,251,252,253", "endColumns": "12,106,104,79,106,99,97,114,82,65,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,77,88,82,79,144,82,81,137,90,82,51,52,65,70,79,70,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82,84,76,76", "endOffsets": "922,4007,4112,4192,4299,4399,5228,5343,5426,7951,8018,13746,14134,14195,14283,14346,14412,14476,14547,14610,14664,14773,14832,14895,14949,15023,15148,15728,15806,15895,15978,16058,16203,16286,16368,16506,16597,16680,16732,16785,16851,16922,17002,17073,17153,17231,17309,17382,17457,17564,17651,17738,17829,17922,17994,18070,18162,18213,18295,18361,18445,18531,18593,18657,18720,18788,18895,19004,19100,19205,19261,19318,20652,21244,21321,21398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6ee598c7bab1dd2859d5d01f2229eaf5\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-eu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6449", "endColumns": "142", "endOffsets": "6587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c9fc1356237c73a36541687fd82ee2b5\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-eu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,244,292,365,435,509,633,697,847,951,1083,1135,1185,1303,1394,1434,1528,1562,1598,1654,1745,1790", "endColumns": "44,47,72,69,73,123,63,149,103,131,51,49,117,90,39,93,33,35,55,90,44,55", "endOffsets": "243,291,364,434,508,632,696,846,950,1082,1134,1184,1302,1393,1433,1527,1561,1597,1653,1744,1789,1845"}, "to": {"startLines": "228,229,230,231,232,233,234,235,236,237,238,239,240,241,243,244,245,246,247,248,249,256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19323,19372,19424,19501,19575,19653,19781,19849,20003,20111,20247,20303,20357,20479,20657,20701,20799,20837,20877,20937,21032,21582", "endColumns": "48,51,76,73,77,127,67,153,107,135,55,53,121,94,43,97,37,39,59,94,48,59", "endOffsets": "19367,19419,19496,19570,19648,19776,19844,19998,20106,20242,20298,20352,20474,20569,20696,20794,20832,20872,20932,21027,21076,21637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6a4b12df2937b548059e098326cd7bcc\\transformed\\jetified-play-services-wallet-18.1.3\\res\\values-eu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "73", "endOffsets": "275"}, "to": {"startLines": "255", "startColumns": "4", "startOffsets": "21504", "endColumns": "77", "endOffsets": "21577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0cb78797bbebcc5583f5b3648f5d6ccd\\transformed\\browser-1.8.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,257,370", "endColumns": "99,101,112,104", "endOffsets": "150,252,365,470"}, "to": {"startLines": "85,159,160,161", "startColumns": "4,4,4,4", "startOffsets": "7706,13751,13853,13966", "endColumns": "99,101,112,104", "endOffsets": "7801,13848,13961,14066"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8e1703580f38993d5096d25ba35ecdf1\\transformed\\jetified-media3-ui-1.4.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,657,741,827,902,1001,1092,1187,1255,1351,1447,1514,1586,1651,1722,1849,1971,2094,2163,2252,2324,2419,2519,2621,2687,2754,2807,2865,2914,2975,3037,3109,3173,3240,3305,3369,3436,3490,3557,3638,3719,3775", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,83,85,74,98,90,94,67,95,95,66,71,64,70,126,121,122,68,88,71,94,99,101,65,66,52,57,48,60,61,71,63,66,64,63,66,53,66,80,80,55,67", "endOffsets": "281,470,652,736,822,897,996,1087,1182,1250,1346,1442,1509,1581,1646,1717,1844,1966,2089,2158,2247,2319,2414,2514,2616,2682,2749,2802,2860,2909,2970,3032,3104,3168,3235,3300,3364,3431,3485,3552,3633,3714,3770,3838"}, "to": {"startLines": "2,11,15,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,139,140,141,142,143,144,145,146,147,148,149,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,570,9635,9719,9805,9880,9979,10070,10165,10233,10329,10425,10492,10564,10629,10700,10827,10949,11072,11141,11230,11302,11397,11497,11599,11665,12430,12483,12541,12590,12651,12713,12785,12849,12916,12981,13045,13245,13299,13366,13447,13528,13584", "endLines": "10,14,18,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,139,140,141,142,143,144,145,146,147,148,149,152,153,154,155,156,157", "endColumns": "17,12,12,83,85,74,98,90,94,67,95,95,66,71,64,70,126,121,122,68,88,71,94,99,101,65,66,52,57,48,60,61,71,63,66,64,63,66,53,66,80,80,55,67", "endOffsets": "376,565,747,9714,9800,9875,9974,10065,10160,10228,10324,10420,10487,10559,10624,10695,10822,10944,11067,11136,11225,11297,11392,11492,11594,11660,11727,12478,12536,12585,12646,12708,12780,12844,12911,12976,13040,13107,13294,13361,13442,13523,13579,13647"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e4f7aaf1dd3251b595a6d29fbf072499\\transformed\\core-1.16.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "57,58,59,60,61,62,63,254", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4404,4502,4605,4705,4808,4913,5016,21403", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "4497,4600,4700,4803,4908,5011,5130,21499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\91b5c4a59b6d6a7985119f29ba44ddae\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,187,253,325,402,476,587,685", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "119,182,248,320,397,471,582,680,748"}, "to": {"startLines": "130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11732,11801,11864,11930,12002,12079,12153,12264,12362", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "11796,11859,11925,11997,12074,12148,12259,12357,12425"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\31cc88bd1a811e01477d23dfca8d3955\\transformed\\jetified-play-services-base-18.5.0\\res\\values-eu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,461,581,686,834,955,1075,1179,1353,1457,1616,1740,1890,2046,2108,2169", "endColumns": "99,167,119,104,147,120,119,103,173,103,158,123,149,155,61,60,87", "endOffsets": "292,460,580,685,833,954,1074,1178,1352,1456,1615,1739,1889,2045,2107,2168,2256"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5431,5535,5707,5831,5940,6092,6217,6341,6592,6770,6878,7041,7169,7323,7483,7549,7614", "endColumns": "103,171,123,108,151,124,123,107,177,107,162,127,153,159,65,64,91", "endOffsets": "5530,5702,5826,5935,6087,6212,6336,6444,6765,6873,7036,7164,7318,7478,7544,7609,7701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfbd6dfbd7eb9930845c16c988ee8525\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,119", "endOffsets": "163,283"}, "to": {"startLines": "50,51", "startColumns": "4,4", "startOffsets": "3672,3785", "endColumns": "112,119", "endOffsets": "3780,3900"}}]}]}