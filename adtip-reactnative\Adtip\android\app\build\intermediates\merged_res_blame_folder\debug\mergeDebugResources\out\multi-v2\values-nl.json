{"logs": [{"outputFile": "com.adtip.app.adtip_app-mergeDebugResources-4:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfbd6dfbd7eb9930845c16c988ee8525\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,121", "endOffsets": "164,286"}, "to": {"startLines": "52,53", "startColumns": "4,4", "startOffsets": "3800,3914", "endColumns": "113,121", "endOffsets": "3909,4031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b7e10b62d187296d864392ee64da0a65\\transformed\\jetified-ucrop-2.2.10\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,105,170,229,284,333", "endColumns": "49,64,58,54,48,47", "endOffsets": "100,165,224,279,328,376"}, "to": {"startLines": "287,288,289,290,291,292", "startColumns": "4,4,4,4,4,4", "startOffsets": "23377,23427,23492,23551,23606,23655", "endColumns": "49,64,58,54,48,47", "endOffsets": "23422,23487,23546,23601,23650,23698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8e1703580f38993d5096d25ba35ecdf1\\transformed\\jetified-media3-ui-1.4.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,486,677,766,854,934,1027,1120,1193,1260,1362,1460,1528,1595,1660,1729,1848,1967,2082,2155,2234,2309,2378,2461,2543,2609,2674,2727,2785,2833,2894,2959,3021,3086,3154,3212,3270,3336,3388,3450,3526,3602,3657", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,118,118,114,72,78,74,68,82,81,65,64,52,57,47,60,64,61,64,67,57,57,65,51,61,75,75,54,66", "endOffsets": "281,481,672,761,849,929,1022,1115,1188,1255,1357,1455,1523,1590,1655,1724,1843,1962,2077,2150,2229,2304,2373,2456,2538,2604,2669,2722,2780,2828,2889,2954,3016,3081,3149,3207,3265,3331,3383,3445,3521,3597,3652,3719"}, "to": {"startLines": "2,11,15,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,144,145,146,147,148,149,150,151,152,153,154,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,581,9827,9916,10004,10084,10177,10270,10343,10410,10512,10610,10678,10745,10810,10879,10998,11117,11232,11305,11384,11459,11528,11611,11693,11759,12480,12533,12591,12639,12700,12765,12827,12892,12960,13018,13076,13273,13325,13387,13463,13539,13594", "endLines": "10,14,18,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,144,145,146,147,148,149,150,151,152,153,154,157,158,159,160,161,162", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,118,118,114,72,78,74,68,82,81,65,64,52,57,47,60,64,61,64,67,57,57,65,51,61,75,75,54,66", "endOffsets": "376,576,767,9911,9999,10079,10172,10265,10338,10405,10507,10605,10673,10740,10805,10874,10993,11112,11227,11300,11379,11454,11523,11606,11688,11754,11819,12528,12586,12634,12695,12760,12822,12887,12955,13013,13071,13137,13320,13382,13458,13534,13589,13656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c9fc1356237c73a36541687fd82ee2b5\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,249,293,348,414,485,580,643,756,873,990,1040,1100,1219,1307,1353,1443,1481,1517,1566,1647,1690", "endColumns": "49,43,54,65,70,94,62,112,116,116,49,59,118,87,45,89,37,35,48,80,42,55", "endOffsets": "248,292,347,413,484,579,642,755,872,989,1039,1099,1218,1306,1352,1442,1480,1516,1565,1646,1689,1745"}, "to": {"startLines": "243,244,245,246,247,248,249,250,251,252,253,254,255,256,262,263,264,265,266,267,268,294", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20025,20079,20127,20186,20256,20331,20430,20497,20614,20735,20856,20910,20974,21097,21561,21611,21705,21747,21787,21840,21925,23779", "endColumns": "53,47,58,69,74,98,66,116,120,120,53,63,122,91,49,93,41,39,52,84,46,59", "endOffsets": "20074,20122,20181,20251,20326,20425,20492,20609,20730,20851,20905,20969,21092,21184,21606,21700,21742,21782,21835,21920,21967,23834"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,206,274,348,434,508,584,668,747,819,897,975,1049,1136,1220,1297,1368,1438,1527,1605,1690", "endColumns": "75,74,67,73,85,73,75,83,78,71,77,77,73,86,83,76,70,69,88,77,84,73", "endOffsets": "126,201,269,343,429,503,579,663,742,814,892,970,1044,1131,1215,1292,1363,1433,1522,1600,1685,1759"}, "to": {"startLines": "50,69,167,171,172,193,194,259,260,261,269,274,275,276,277,278,279,280,281,283,284,286", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3663,5507,14061,14310,14384,16020,16094,21326,21410,21489,21972,22364,22442,22516,22603,22687,22764,22835,22905,23095,23173,23303", "endColumns": "75,74,67,73,85,73,75,83,78,71,77,77,73,86,83,76,70,69,88,77,84,73", "endOffsets": "3734,5577,14124,14379,14465,16089,16165,21405,21484,21556,22045,22437,22511,22598,22682,22759,22830,22900,22989,23168,23253,23372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6a4b12df2937b548059e098326cd7bcc\\transformed\\jetified-play-services-wallet-18.1.3\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "71", "endOffsets": "273"}, "to": {"startLines": "293", "startColumns": "4", "startOffsets": "23703", "endColumns": "75", "endOffsets": "23774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ef18ad19ff26599d64ec0eff4ea7dc70\\transformed\\appcompat-1.7.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "941,1059,1164,1271,1356,1460,1580,1658,1734,1826,1920,2015,2109,2209,2303,2399,2494,2586,2678,2760,2871,2974,3073,3188,3302,3405,3560,22050", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "1054,1159,1266,1351,1455,1575,1653,1729,1821,1915,2010,2104,2204,2298,2394,2489,2581,2673,2755,2866,2969,3068,3183,3297,3400,3555,3658,22128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\31cc88bd1a811e01477d23dfca8d3955\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,444,568,675,838,961,1080,1182,1356,1458,1623,1745,1904,2082,2146,2205", "endColumns": "103,146,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,443,567,674,837,960,1079,1181,1355,1457,1622,1744,1903,2081,2145,2204,2279"}, "to": {"startLines": "70,71,72,73,74,75,76,77,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5582,5690,5841,5969,6080,6247,6374,6497,6746,6924,7030,7199,7325,7488,7670,7738,7801", "endColumns": "107,150,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "5685,5836,5964,6075,6242,6369,6492,6598,6919,7025,7194,7320,7483,7665,7733,7796,7875"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f9b6829f7f71dcf7ed8c3a43ef2febe5\\transformed\\jetified-exoplayer-ui-2.19.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "54,55", "startColumns": "4,4", "startOffsets": "3336,3401", "endColumns": "64,65", "endOffsets": "3396,3462"}, "to": {"startLines": "155,156", "startColumns": "4,4", "startOffsets": "13142,13207", "endColumns": "64,65", "endOffsets": "13202,13268"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6ee598c7bab1dd2859d5d01f2229eaf5\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "78", "startColumns": "4", "startOffsets": "6603", "endColumns": "142", "endOffsets": "6741"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9eda951e918e947e7d9ddd1ba9b2c563\\transformed\\jetified-media3-session-1.4.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,234,310,417,515,605,700,800,897,974,1039,1139,1222,1319,1397,1488,1563,1656,1727,1797,1882,1968,2065", "endColumns": "81,96,75,106,97,89,94,99,96,76,64,99,82,96,77,90,74,92,70,69,84,85,96,98", "endOffsets": "132,229,305,412,510,600,695,795,892,969,1034,1134,1217,1314,1392,1483,1558,1651,1722,1792,1877,1963,2060,2159"}, "to": {"startLines": "91,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,187,188,189,190,191,192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8102,8308,8405,8481,8588,8686,8776,8871,8971,9068,9145,9210,9310,9393,9490,9568,9659,9734,15512,15583,15653,15738,15824,15921", "endColumns": "81,96,75,106,97,89,94,99,96,76,64,99,82,96,77,90,74,92,70,69,84,85,96,98", "endOffsets": "8179,8400,8476,8583,8681,8771,8866,8966,9063,9140,9205,9305,9388,9485,9563,9654,9729,9822,15578,15648,15733,15819,15916,16015"}}, {"source": "C:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,116,177,235,294,337,403,452,514,570,615", "endColumns": "60,60,57,58,42,65,48,61,55,44,59", "endOffsets": "111,172,230,289,332,398,447,509,565,610,670"}, "to": {"startLines": "51,89,90,168,169,195,196,197,257,285,295", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3739,7983,8044,14129,14188,16170,16236,16285,21189,23258,23839", "endColumns": "60,60,57,58,42,65,48,61,55,44,59", "endOffsets": "3795,8039,8097,14183,14226,16231,16280,16342,21240,23298,23894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e4f7aaf1dd3251b595a6d29fbf072499\\transformed\\core-1.16.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "59,60,61,62,63,64,65,282", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4473,4575,4677,4777,4877,4984,5088,22994", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "4570,4672,4772,4872,4979,5083,5202,23090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\91b5c4a59b6d6a7985119f29ba44ddae\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,254,321,398,467,556,639", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "121,185,249,316,393,462,551,634,706"}, "to": {"startLines": "135,136,137,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11824,11895,11959,12023,12090,12167,12236,12325,12408", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "11890,11954,12018,12085,12162,12231,12320,12403,12475"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0cb78797bbebcc5583f5b3648f5d6ccd\\transformed\\browser-1.8.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "88,164,165,166", "startColumns": "4,4,4,4", "startOffsets": "7880,13750,13851,13962", "endColumns": "102,100,110,98", "endOffsets": "7978,13846,13957,14056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dd11809084d63593099820b405d61701\\transformed\\material-1.12.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1066,1130,1219,1298,1361,1454,1516,1582,1640,1713,1777,1833,1955,2012,2074,2130,2206,2340,2425,2504,2602,2688,2774,2912,2993,3072,3196,3286,3363,3420,3471,3537,3615,3698,3769,3845,3920,3999,4072,4143,4252,4346,4424,4513,4603,4677,4758,4845,4898,4977,5044,5125,5209,5271,5335,5398,5469,5577,5689,5791,5902,5963,6018,6099,6182,6258", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "264,350,432,509,607,701,798,920,1001,1061,1125,1214,1293,1356,1449,1511,1577,1635,1708,1772,1828,1950,2007,2069,2125,2201,2335,2420,2499,2597,2683,2769,2907,2988,3067,3191,3281,3358,3415,3466,3532,3610,3693,3764,3840,3915,3994,4067,4138,4247,4341,4419,4508,4598,4672,4753,4840,4893,4972,5039,5120,5204,5266,5330,5393,5464,5572,5684,5786,5897,5958,6013,6094,6177,6253,6325"}, "to": {"startLines": "19,54,55,56,57,58,66,67,68,92,93,163,170,173,174,175,176,177,178,179,180,181,182,183,184,185,186,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,258,271,272,273", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "772,4036,4122,4204,4281,4379,5207,5304,5426,8184,8244,13661,14231,14470,14533,14626,14688,14754,14812,14885,14949,15005,15127,15184,15246,15302,15378,16347,16432,16511,16609,16695,16781,16919,17000,17079,17203,17293,17370,17427,17478,17544,17622,17705,17776,17852,17927,18006,18079,18150,18259,18353,18431,18520,18610,18684,18765,18852,18905,18984,19051,19132,19216,19278,19342,19405,19476,19584,19696,19798,19909,19970,21245,22133,22216,22292", "endLines": "22,54,55,56,57,58,66,67,68,92,93,163,170,173,174,175,176,177,178,179,180,181,182,183,184,185,186,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,258,271,272,273", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "936,4117,4199,4276,4374,4468,5299,5421,5502,8239,8303,13745,14305,14528,14621,14683,14749,14807,14880,14944,15000,15122,15179,15241,15297,15373,15507,16427,16506,16604,16690,16776,16914,16995,17074,17198,17288,17365,17422,17473,17539,17617,17700,17771,17847,17922,18001,18074,18145,18254,18348,18426,18515,18605,18679,18760,18847,18900,18979,19046,19127,19211,19273,19337,19400,19471,19579,19691,19793,19904,19965,20020,21321,22211,22287,22359"}}]}]}