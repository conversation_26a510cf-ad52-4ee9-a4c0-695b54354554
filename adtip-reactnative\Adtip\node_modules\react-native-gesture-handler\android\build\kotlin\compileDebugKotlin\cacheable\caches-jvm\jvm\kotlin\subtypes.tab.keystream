#com.facebook.react.BaseReactPackage2com.facebook.react.ViewManagerOnDemandReactPackage0com.swmansion.gesturehandler.core.GestureHandler8com.swmansion.gesturehandler.core.GestureHandler.Factoryjava.lang.ExceptionWcom.swmansion.gesturehandler.core.NativeViewGestureHandler.NativeViewGestureHandlerHookkotlin.Enum-com.facebook.react.uimanager.ViewGroupManagerFcom.facebook.react.viewmanagers.RNGestureHandlerButtonManagerInterfaceandroid.view.ViewGroup com.facebook.react.ReactRootView)com.facebook.react.uimanager.events.Event6com.swmansion.gesturehandler.core.OnTouchEventListenerEcom.swmansion.gesturehandler.core.GestureHandlerInteractionController=com.swmansion.gesturehandler.NativeRNGestureHandlerModuleSpec/com.swmansion.common.GestureHandlerStateManager8com.swmansion.gesturehandler.core.GestureHandlerRegistry,com.facebook.react.views.view.ReactViewGroupHcom.facebook.react.viewmanagers.RNGestureHandlerRootViewManagerInterface9com.swmansion.gesturehandler.core.ViewConfigurationHelperOcom.swmansion.gesturehandler.react.eventbuilders.GestureHandlerEventDataBuilder                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            